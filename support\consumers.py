"""
WebSocket consumers for real-time chat functionality
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import ChatSession, ChatMessage, SupportNotification
from .services import ChatService

logger = logging.getLogger(__name__)
User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for live chat functionality"""
    
    async def connect(self):
        """Handle WebSocket connection"""
        try:
            # Get session ID from URL
            self.session_id = self.scope['url_route']['kwargs']['session_id']
            self.room_group_name = f'chat_{self.session_id}'
            
            # Get user from scope
            self.user = self.scope['user']
            
            if not self.user.is_authenticated:
                await self.close()
                return
            
            # Verify user has access to this chat session
            session = await self.get_chat_session(self.session_id)
            if not session:
                await self.close()
                return
            
            # Check if user is participant in this chat
            if session.user != self.user and session.agent != self.user:
                await self.close()
                return
            
            self.session = session
            
            # Join room group
            if self.channel_layer is not None:
                await self.channel_layer.group_add(
                    self.room_group_name,
                    self.channel_name
                )
            else:
                logger.error("Channel layer is not configured properly")
                await self.close()
                return
            
            await self.accept()
            
            # Send connection confirmation
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'session_id': str(self.session_id),
                'status': session.status
            }))
            
            logger.info(f'User {self.user.id} connected to chat {self.session_id}')
            
        except Exception as e:
            logger.error(f'Error in chat connect: {e}')
            await self.close()
    
    async def disconnect(self, code):
        """Handle WebSocket disconnection"""
        # Acknowledge unused parameter required by base class
        _ = code
        try:
            # Leave room group
            if self.channel_layer is not None:
                await self.channel_layer.group_discard(
                    self.room_group_name,
                    self.channel_name
                )

            logger.info(f'User {self.user.id} disconnected from chat {self.session_id}')

        except Exception as e:
            logger.error(f'Error in chat disconnect: {e}')
    
    async def receive(self, text_data=None, bytes_data=None):
        """Handle messages from WebSocket"""
        # Acknowledge unused parameter required by base class
        _ = bytes_data
        if text_data is None:
            return
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'chat_message':
                await self.handle_chat_message(data)
            elif message_type == 'typing_start':
                await self.handle_typing_start()
            elif message_type == 'typing_stop':
                await self.handle_typing_stop()
            elif message_type == 'join_chat':
                await self.handle_join_chat()
            elif message_type == 'end_chat':
                await self.handle_end_chat()
            else:
                logger.warning(f'Unknown message type: {message_type}')
                
        except json.JSONDecodeError:
            logger.error('Invalid JSON received in chat consumer')
        except Exception as e:
            logger.error(f'Error handling chat message: {e}')
    
    async def handle_chat_message(self, data):
        """Handle incoming chat message"""
        try:
            message_content = data.get('message', '').strip()
            if not message_content:
                return
            
            # Create message in database
            message = await self.create_chat_message(
                session=self.session,
                sender=self.user,
                content=message_content
            )
            
            if message:
                # Broadcast message to room group
                if self.channel_layer is not None:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'chat_message_broadcast',
                            'message': {
                                'id': str(message.id),
                                'content': message.content,
                                'sender_id': str(message.sender.id),
                                'sender_name': message.sender.get_full_name() or message.sender.phone_number,
                                'is_from_agent': message.is_from_agent,
                                'is_system_message': message.is_system_message,
                                'created_at': message.created_at.isoformat()
                            }
                        }
                    )
                
        except Exception as e:
            logger.error(f'Error handling chat message: {e}')
    
    async def handle_typing_start(self):
        """Handle typing indicator start"""
        if self.channel_layer is not None:
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'typing_indicator',
                    'user_id': str(self.user.id),
                    'user_name': self.user.get_full_name() or self.user.phone_number,
                    'is_typing': True
                }
            )

    async def handle_typing_stop(self):
        """Handle typing indicator stop"""
        if self.channel_layer is not None:
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'typing_indicator',
                    'user_id': str(self.user.id),
                    'user_name': self.user.get_full_name() or self.user.phone_number,
                    'is_typing': False
                }
            )
    
    async def handle_join_chat(self):
        """Handle agent joining chat"""
        if not self.user.is_staff:
            return
        
        try:
            # Assign agent to chat session
            success = await self.assign_agent_to_chat(self.session, self.user)
            
            if success:
                # Broadcast agent joined message
                if self.channel_layer is not None:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'agent_joined',
                            'agent_name': self.user.get_full_name() or self.user.phone_number
                        }
                    )
                
        except Exception as e:
            logger.error(f'Error joining chat: {e}')
    
    async def handle_end_chat(self):
        """Handle ending chat session"""
        try:
            # End chat session
            success = await self.end_chat_session(self.session, self.user)
            
            if success:
                # Broadcast chat ended message
                if self.channel_layer is not None:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'chat_ended',
                            'ended_by': self.user.get_full_name() or self.user.phone_number
                        }
                    )
                
        except Exception as e:
            logger.error(f'Error ending chat: {e}')
    
    # WebSocket message handlers
    async def chat_message_broadcast(self, event):
        """Send chat message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message']
        }))
    
    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        # Don't send typing indicator to the sender
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'typing_indicator',
                'user_name': event['user_name'],
                'is_typing': event['is_typing']
            }))
    
    async def agent_joined(self, event):
        """Send agent joined notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'agent_joined',
            'agent_name': event['agent_name']
        }))
    
    async def chat_ended(self, event):
        """Send chat ended notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_ended',
            'ended_by': event['ended_by']
        }))
    
    async def system_message(self, event):
        """Send system message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': event['message']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_chat_session(self, session_id):
        """Get chat session from database"""
        try:
            return ChatSession.objects.select_related('user', 'agent').get(id=session_id)
        except ChatSession.DoesNotExist:
            return None
    
    @database_sync_to_async
    def create_chat_message(self, session, sender, content):
        """Create chat message in database"""
        try:
            chat_service = ChatService()
            return chat_service.send_message(
                session=session,
                sender=sender,
                content=content
            )
        except Exception as e:
            logger.error(f'Error creating chat message: {e}')
            return None
    
    @database_sync_to_async
    def assign_agent_to_chat(self, session, agent):
        """Assign agent to chat session"""
        try:
            chat_service = ChatService()
            return chat_service.assign_agent_to_chat(session, agent)
        except Exception as e:
            logger.error(f'Error assigning agent to chat: {e}')
            return False
    
    @database_sync_to_async
    def end_chat_session(self, session, ended_by):
        """End chat session"""
        try:
            chat_service = ChatService()
            return chat_service.end_chat_session(session, ended_by)
        except Exception as e:
            logger.error(f'Error ending chat session: {e}')
            return False


class SupportNotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for support notifications"""
    
    async def connect(self):
        """Handle WebSocket connection for notifications"""
        try:
            self.user = self.scope['user']
            
            if not self.user.is_authenticated:
                await self.close()
                return
            
            # Create user-specific notification group
            self.notification_group_name = f'notifications_{self.user.id}'
            
            # Join notification group
            if self.channel_layer is not None:
                await self.channel_layer.group_add(
                    self.notification_group_name,
                    self.channel_name
                )
            else:
                logger.error("Channel layer is not configured properly")
                await self.close()
                return
            
            await self.accept()
            
            logger.info(f'User {self.user.id} connected to notifications')
            
        except Exception as e:
            logger.error(f'Error in notification connect: {e}')
            await self.close()
    
    async def disconnect(self, code):
        """Handle WebSocket disconnection for notifications"""
        # Acknowledge unused parameter required by base class
        _ = code
        try:
            # Leave notification group
            if self.channel_layer is not None:
                await self.channel_layer.group_discard(
                    self.notification_group_name,
                    self.channel_name
                )

            logger.info(f'User {self.user.id} disconnected from notifications')

        except Exception as e:
            logger.error(f'Error in notification disconnect: {e}')

    async def receive(self, text_data=None, bytes_data=None):
        """Handle messages from WebSocket"""
        # Acknowledge unused parameter required by base class
        _ = bytes_data
        if text_data is None:
            return
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'mark_notification_read':
                await self.handle_mark_notification_read(data)
            else:
                logger.warning(f'Unknown notification message type: {message_type}')

        except json.JSONDecodeError:
            logger.error('Invalid JSON received in notification consumer')
        except Exception as e:
            logger.error(f'Error handling notification message: {e}')
    
    async def handle_mark_notification_read(self, data):
        """Mark notification as read"""
        try:
            notification_id = data.get('notification_id')
            if notification_id:
                await self.mark_notification_read(notification_id)
                
        except Exception as e:
            logger.error(f'Error marking notification as read: {e}')
    
    # WebSocket message handlers
    async def support_notification(self, event):
        """Send support notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'support_notification',
            'notification': event['notification']
        }))
    
    async def ticket_update(self, event):
        """Send ticket update notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'ticket_update',
            'ticket': event['ticket']
        }))
    
    async def chat_notification(self, event):
        """Send chat notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_notification',
            'notification': event['notification']
        }))
    
    # Database operations
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """Mark notification as read in database"""
        try:
            notification = SupportNotification.objects.get(
                id=notification_id,
                recipient=self.user
            )
            notification.mark_as_read()
            return True
        except SupportNotification.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f'Error marking notification as read: {e}')
            return False
