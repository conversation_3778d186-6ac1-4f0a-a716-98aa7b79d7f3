"""
API views for payment functionality
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
import json
import logging

from .models import Transaction
from .services import PaymentService, PaymentException

logger = logging.getLogger(__name__)


# Class-based API Views
class DepositAPIView(APIView):
    """API view for deposit functionality"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        return Response({"message": "Deposit API - Coming Soon"}, status=status.HTTP_200_OK)


class WithdrawAPIView(APIView):
    """API view for withdrawal functionality"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        return Response({"message": "Withdraw API - Coming Soon"}, status=status.HTTP_200_OK)


class BalanceAPIView(APIView):
    """API view for balance retrieval"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({
            "balance": str(request.user.balance),
            "currency": "KES"
        }, status=status.HTTP_200_OK)


class PaymentHistoryAPIView(APIView):
    """API view for payment history"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({"message": "Payment History API - Coming Soon"}, status=status.HTTP_200_OK)


class PaymentMethodsAPIView(APIView):
    """API view for payment methods"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        return Response({"message": "Payment Methods API - Coming Soon"}, status=status.HTTP_200_OK)


class AddPaymentMethodAPIView(APIView):
    """API view for adding payment methods"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        return Response({"message": "Add Payment Method API - Coming Soon"}, status=status.HTTP_200_OK)


class PaymentMethodDetailAPIView(APIView):
    """API view for payment method details"""
    permission_classes = [IsAuthenticated]

    def get(self, request, method_id):
        return Response({"message": f"Payment Method {method_id} API - Coming Soon"}, status=status.HTTP_200_OK)


class TransactionDetailAPIView(APIView):
    """API view for transaction details"""
    permission_classes = [IsAuthenticated]

    def get(self, request, transaction_id):
        return Response({"message": f"Transaction {transaction_id} API - Coming Soon"}, status=status.HTTP_200_OK)


class InitiateMpesaPaymentAPIView(APIView):
    """API view for initiating M-Pesa payments"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        return Response({"message": "Initiate M-Pesa Payment API - Coming Soon"}, status=status.HTTP_200_OK)


class MpesaCallbackAPIView(APIView):
    """API view for M-Pesa STK Push callbacks"""

    def post(self, request):
        try:
            callback_data = request.data

            # Process M-Pesa callback
            from .services import PaymentService
            payment_service = PaymentService()
            payment_service.process_mpesa_callback(callback_data)

            return Response({'status': 'success'}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"M-Pesa callback error: {e}")
            return Response({
                'error': 'Callback processing failed'
            }, status=status.HTTP_400_BAD_REQUEST)


class MpesaB2CCallbackAPIView(APIView):
    """API view for M-Pesa B2C withdrawal callbacks"""

    def post(self, request):
        try:
            callback_data = request.data

            # Process M-Pesa B2C callback
            from .services import PaymentService
            payment_service = PaymentService()
            payment_service.process_mpesa_b2c_callback(callback_data)

            return Response({'status': 'success'}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"M-Pesa B2C callback error: {e}")
            return Response({
                'error': 'B2C callback processing failed'
            }, status=status.HTTP_400_BAD_REQUEST)


class MpesaB2CTimeoutAPIView(APIView):
    """API view for M-Pesa B2C timeout notifications"""

    def post(self, request):
        try:
            timeout_data = request.data
            logger.warning(f"M-Pesa B2C timeout received: {timeout_data}")

            # Handle timeout - could mark transaction as failed after timeout
            # For now, just log it

            return Response({'status': 'success'}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"M-Pesa B2C timeout error: {e}")
            return Response({
                'error': 'Timeout processing failed'
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateStripePaymentIntentAPIView(APIView):
    """API view for creating Stripe payment intents"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            amount = request.data.get('amount')
            if not amount:
                return Response({
                    'error': 'Amount is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create a pending transaction
            from .services import PaymentService
            payment_service = PaymentService()

            transaction_obj = payment_service.initiate_deposit(
                user=request.user,
                amount=amount,
                payment_method='card'
            )

            return Response({
                'transaction_id': str(transaction_obj.id),
                'client_secret': transaction_obj.metadata.get('stripe_client_secret'),
                'amount': str(transaction_obj.amount),
                'status': transaction_obj.status
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Failed to create payment intent: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StripeWebhookAPIView(APIView):
    """API view for Stripe webhooks"""

    def post(self, request):
        try:
            payload = request.body
            sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

            if not sig_header:
                return Response({
                    'error': 'Missing Stripe signature'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Process webhook
            from .services import PaymentService
            payment_service = PaymentService()
            payment_service.process_stripe_webhook(payload, sig_header)

            return Response({'status': 'success'}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Stripe webhook error: {e}")
            return Response({
                'error': 'Webhook processing failed'
            }, status=status.HTTP_400_BAD_REQUEST)


# Function-based API Views (existing)


@login_required
@require_http_methods(["GET"])
def transaction_status_api(request, transaction_id):
    """
    API endpoint to check transaction status
    """
    try:
        transaction = Transaction.objects.get(id=transaction_id, user=request.user)
        
        # Get detailed status info for withdrawals
        if transaction.transaction_type == 'withdrawal':
            payment_service = PaymentService()
            status_info = payment_service.withdrawal_service.get_withdrawal_status_info(transaction)
            
            response_data = {
                'id': str(transaction.id),
                'status': transaction.status,
                'status_display': transaction.get_status_display(),
                'amount': str(transaction.amount),
                'created_at': transaction.created_at.isoformat(),
                'processed_at': transaction.processed_at.isoformat() if transaction.processed_at else None,
                'payment_method': transaction.payment_method,
                'payment_method_display': transaction.get_payment_method_display(),
                'status_info': status_info
            }
        else:
            response_data = {
                'id': str(transaction.id),
                'status': transaction.status,
                'status_display': transaction.get_status_display(),
                'amount': str(transaction.amount),
                'created_at': transaction.created_at.isoformat(),
                'processed_at': transaction.processed_at.isoformat() if transaction.processed_at else None,
                'payment_method': transaction.payment_method,
                'payment_method_display': transaction.get_payment_method_display()
            }
        
        return JsonResponse(response_data)
        
    except Transaction.DoesNotExist:
        return JsonResponse({'error': 'Transaction not found'}, status=404)
    except Exception as e:
        logger.error(f"Error retrieving transaction status: {e}")
        return JsonResponse({'error': 'An error occurred while retrieving transaction status'}, status=500)


@login_required
@require_http_methods(["POST"])
def cancel_withdrawal_api(request, transaction_id):
    """
    API endpoint to cancel a withdrawal
    """
    try:
        transaction = Transaction.objects.get(
            id=transaction_id, 
            user=request.user,
            transaction_type='withdrawal',
            status__in=['pending', 'processing']
        )
        
        # Parse request body
        try:
            data = json.loads(request.body)
            reason = data.get('reason', 'User requested cancellation')
        except json.JSONDecodeError:
            reason = 'User requested cancellation'
        
        # Cancel withdrawal
        payment_service = PaymentService()
        cancelled_transaction = payment_service.cancel_withdrawal(
            transaction_id=transaction.id,
            reason=reason
        )
        
        # Send notification
        payment_service.send_withdrawal_notification(
            cancelled_transaction, 
            notification_type='cancellation'
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Withdrawal cancelled successfully',
            'transaction': {
                'id': str(cancelled_transaction.id),
                'status': cancelled_transaction.status,
                'status_display': cancelled_transaction.get_status_display()  # type: ignore
            }
        })
        
    except Transaction.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Transaction not found or not eligible for cancellation'
        }, status=404)
    except PaymentException as e:
        logger.error(f"Payment exception when cancelling withdrawal: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)
    except Exception as e:
        logger.error(f"Error cancelling withdrawal: {e}")
        return JsonResponse({
            'success': False,
            'error': 'An error occurred while cancelling the withdrawal'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_withdrawal_callback(request):
    """
    Callback endpoint for M-Pesa B2C withdrawal API
    """
    try:
        callback_data = json.loads(request.body.decode('utf-8'))
        logger.info(f"M-Pesa withdrawal callback received: {callback_data}")
        
        # Process callback using payment service
        payment_service = PaymentService()
        # This would be implemented in a real system:
        # payment_service.process_mpesa_withdrawal_callback(callback_data)
        
        return JsonResponse({'ResultCode': 0, 'ResultDesc': 'Success'})
        
    except Exception as e:
        logger.error(f"Error processing M-Pesa withdrawal callback: {e}")
        return JsonResponse({'ResultCode': 1, 'ResultDesc': 'Failed'})


class BankTransferVerificationAPIView(APIView):
    """API view for bank transfer verification"""
    permission_classes = [IsAuthenticated]

    def post(self, request, transaction_id):
        try:
            verification_data = {
                'amount': request.data.get('amount'),
                'reference_number': request.data.get('reference_number'),
                'deposit_date': request.data.get('deposit_date'),
                'bank_reference': request.data.get('bank_reference'),
                'method': 'manual'
            }

            # Validate required fields
            required_fields = ['amount', 'reference_number', 'bank_reference']
            for field in required_fields:
                if not verification_data.get(field):
                    return Response({
                        'error': f'{field} is required'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # Process verification
            from .services import PaymentService
            payment_service = PaymentService()
            result = payment_service.verify_bank_transfer(transaction_id, verification_data)

            if result['verified']:
                return Response({
                    'status': 'success',
                    'message': 'Bank transfer verified successfully',
                    'transaction_id': result['transaction_id'],
                    'amount_credited': result['amount_credited']
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'status': 'failed',
                    'error': result['error']
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Bank transfer verification error: {e}")
            return Response({
                'error': 'Verification processing failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BankTransferInstructionsAPIView(APIView):
    """API view for getting bank transfer instructions"""
    permission_classes = [IsAuthenticated]

    def get(self, request, transaction_id):
        try:
            # Get transaction
            transaction = Transaction.objects.get(
                id=transaction_id,
                user=request.user,
                transaction_type='deposit',
                payment_method='bank_transfer'
            )

            # Get instructions from metadata
            instructions = transaction.metadata.get('bank_instructions', {})

            return Response({
                'transaction_id': str(transaction.id),
                'amount': str(transaction.amount),
                'status': transaction.status,
                'instructions': instructions,
                'created_at': transaction.created_at.isoformat()
            }, status=status.HTTP_200_OK)

        except Transaction.DoesNotExist:
            return Response({
                'error': 'Transaction not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Bank transfer instructions error: {e}")
            return Response({
                'error': 'Failed to retrieve instructions'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)