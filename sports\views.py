from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from .models import Sport, Event, Market, Odds


def sports_list_view(request):
    """Display list of all active sports with event counts"""
    sports = Sport.active.annotate(
        active_events_count=Count('events', filter=Q(events__status__in=['upcoming', 'live']))
    ).order_by('display_order', 'name')
    
    # Get featured events across all sports
    featured_events = Event.active.filter(is_featured=True).select_related('sport')[:6]
    
    context = {
        'sports': sports,
        'featured_events': featured_events,
        'page_title': 'Sports Betting'
    }
    return render(request, 'sports/sports_list.html', context)


def sport_detail_view(request, sport_slug):
    """Display events for a specific sport"""
    sport = get_object_or_404(Sport.active, slug=sport_slug)
    
    # Get events with prefetched markets and odds
    events_queryset = Event.active.filter(sport=sport).select_related('sport').prefetch_related(
        Prefetch('markets', queryset=Market.active.prefetch_related('odds'))
    )
    
    # Filter by status if requested
    status_filter = request.GET.get('status', 'all')
    if status_filter in ['upcoming', 'live']:
        events_queryset = events_queryset.filter(status=status_filter)
    
    # Search functionality
    search_query = request.GET.get('search', '').strip()
    if search_query:
        events_queryset = events_queryset.filter(
            Q(home_team__icontains=search_query) |
            Q(away_team__icontains=search_query) |
            Q(league__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(events_queryset, 20)
    page_number = request.GET.get('page')
    events = paginator.get_page(page_number)
    
    context = {
        'sport': sport,
        'events': events,
        'status_filter': status_filter,
        'search_query': search_query,
        'page_title': f'{sport.name} Events'
    }
    return render(request, 'sports/sport_detail.html', context)


def sport_events_view(request, sport_slug):
    """API endpoint for fetching sport events (AJAX)"""
    sport = get_object_or_404(Sport.active, slug=sport_slug)
    
    events = Event.active.filter(sport=sport).select_related('sport')
    
    # Apply filters
    status = request.GET.get('status')
    if status and status in ['upcoming', 'live', 'finished']:
        events = events.filter(status=status)
    
    league = request.GET.get('league')
    if league:
        events = events.filter(league__icontains=league)
    
    # Serialize events data
    events_data = []
    for event in events[:50]:  # Limit to 50 events
        events_data.append({
            'id': event.id,
            'home_team': event.home_team,
            'away_team': event.away_team,
            'start_time': event.start_time.isoformat(),
            'status': event.status,
            'league': event.league,
            'score': event.get_score_display(),
            'is_live': event.is_live,
            'url': event.get_absolute_url()
        })
    
    return JsonResponse({
        'events': events_data,
        'count': len(events_data)
    })


def event_detail_view(request, event_id):
    """Display detailed view of a specific event with markets and odds"""
    event = get_object_or_404(
        Event.objects.select_related('sport'),
        id=event_id
    )

    # Group markets by type for better display
    markets_by_type = {}
    # Get active markets for this event with prefetched odds
    event_markets = Market.active.filter(event=event).prefetch_related('odds')
    for market in event_markets:
        market_type = market.get_market_type_display()
        if market_type not in markets_by_type:
            markets_by_type[market_type] = []
        markets_by_type[market_type].append(market)

    context = {
        'event': event,
        'markets_by_type': markets_by_type,
        'page_title': f'{event.home_team} vs {event.away_team}'
    }
    return render(request, 'sports/event_detail.html', context)


def event_markets_view(request, event_id):
    """API endpoint for fetching event markets and odds (AJAX)"""
    # request parameter is required by Django view signature but not used in this function
    event = get_object_or_404(Event, id=event_id)

    markets = Market.active.filter(event=event).prefetch_related('odds')

    markets_data = []
    for market in markets:
        odds_data = []
        for odds in market.odds.filter(is_active=True):
            odds_data.append({
                'id': odds.pk,  # Use pk instead of id for better IDE compatibility
                'selection': odds.selection,
                'odds_value': str(odds.odds_value),
                'has_changed': odds.has_changed,
                'change_direction': odds.change_direction
            })

        markets_data.append({
            'id': market.pk,  # Use pk instead of id for better IDE compatibility
            'name': market.name,
            'market_type': market.market_type,
            'parameter': str(market.parameter) if market.parameter else None,
            'odds': odds_data
        })

    return JsonResponse({
        'event_id': event.pk,  # Use pk instead of id for better IDE compatibility
        'markets': markets_data
    })


def market_odds_view(request, market_id):
    """API endpoint for fetching specific market odds (AJAX)"""
    # request parameter is required by Django view signature but not used in this function
    market = get_object_or_404(Market.active, id=market_id)

    odds = market.odds.filter(is_active=True).order_by('display_order', 'selection')

    odds_data = []
    for odd in odds:
        odds_data.append({
            'id': odd.pk,  # Use pk instead of id for better IDE compatibility
            'selection': odd.selection,
            'odds_value': str(odd.odds_value),
            'previous_odds': str(odd.previous_odds) if odd.previous_odds else None,
            'has_changed': odd.has_changed,
            'change_direction': odd.change_direction,
            'implied_probability': odd.get_implied_probability(),
            'last_updated': odd.last_updated.isoformat()
        })

    return JsonResponse({
        'market_id': market.pk,  # Use pk instead of id for better IDE compatibility
        'market_name': market.name,
        'odds': odds_data
    })


def search_events_view(request):
    """Search events across all sports"""
    query = request.GET.get('q', '').strip()
    sport_filter = request.GET.get('sport')
    status_filter = request.GET.get('status', 'active')
    
    events = Event.objects.select_related('sport')
    
    # Apply status filter
    if status_filter == 'active':
        events = events.filter(status__in=['upcoming', 'live'])
    elif status_filter in ['upcoming', 'live', 'finished']:
        events = events.filter(status=status_filter)
    
    # Apply sport filter
    if sport_filter:
        events = events.filter(sport__slug=sport_filter)
    
    # Apply search query
    if query:
        events = events.filter(
            Q(home_team__icontains=query) |
            Q(away_team__icontains=query) |
            Q(league__icontains=query)
        )
    
    # Pagination
    paginator = Paginator(events, 20)
    page_number = request.GET.get('page')
    events_page = paginator.get_page(page_number)
    
    # Get all sports for filter dropdown
    sports = Sport.active.all()
    
    context = {
        'events': events_page,
        'sports': sports,
        'query': query,
        'sport_filter': sport_filter,
        'status_filter': status_filter,
        'page_title': f'Search Results: {query}' if query else 'Search Events'
    }
    return render(request, 'sports/search_events.html', context)


def filter_events_view(request):
    """Filter events with advanced options"""
    # Get filter parameters
    sport_ids = request.GET.getlist('sports')
    leagues = request.GET.getlist('leagues')
    status = request.GET.get('status', 'active')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    events = Event.objects.select_related('sport')
    
    # Apply filters
    if status == 'active':
        events = events.filter(status__in=['upcoming', 'live'])
    elif status in ['upcoming', 'live', 'finished']:
        events = events.filter(status=status)
    
    if sport_ids:
        events = events.filter(sport__id__in=sport_ids)
    
    if leagues:
        events = events.filter(league__in=leagues)
    
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            events = events.filter(start_time__date__gte=date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            events = events.filter(start_time__date__lte=date_to_obj)
        except ValueError:
            pass
    
    # Pagination
    paginator = Paginator(events, 20)
    page_number = request.GET.get('page')
    events_page = paginator.get_page(page_number)
    
    # Get filter options
    sports = Sport.active.all()
    available_leagues = Event.objects.values_list('league', flat=True).distinct().exclude(league='')
    
    context = {
        'events': events_page,
        'sports': sports,
        'available_leagues': sorted(available_leagues),
        'selected_sports': [int(id) for id in sport_ids if id.isdigit()],
        'selected_leagues': leagues,
        'status_filter': status,
        'date_from': date_from,
        'date_to': date_to,
        'page_title': 'Filter Events'
    }
    return render(request, 'sports/filter_events.html', context)
