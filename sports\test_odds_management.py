from django.test import TestCase
from django.utils import timezone
from django.urls import reverse
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import timedelta, date
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from typing import TYPE_CHECKING
from .models import Sport, Event, Market, Odds
from .services import OddsUpdateService, OddsValidationService

if TYPE_CHECKING:
    from accounts.models import CustomUser

User = get_user_model()


class OddsUpdateServiceTest(TestCase):
    """Test cases for OddsUpdateService"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming',
            external_id='test_event_1'
        )
        self.market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result',
            is_active=True
        )
        self.odds_home = Odds.objects.create(
            market=self.market,
            selection='Home',
            odds_value=Decimal('2.50'),
            is_active=True
        )
        self.odds_draw = Odds.objects.create(
            market=self.market,
            selection='Draw',
            odds_value=Decimal('3.20'),
            is_active=True
        )
        self.odds_away = Odds.objects.create(
            market=self.market,
            selection='Away',
            odds_value=Decimal('2.80'),
            is_active=True
        )
        self.service = OddsUpdateService()

    def test_update_single_odds_success(self):
        """Test successful single odds update"""
        new_odds = Decimal('2.75')
        result = self.service._update_single_odds(
            self.market.id,
            'Home',
            new_odds
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['odds']['new_odds'], str(new_odds))
        
        # Verify odds was updated in database
        self.odds_home.refresh_from_db()
        self.assertEqual(self.odds_home.odds_value, new_odds)
        self.assertEqual(self.odds_home.previous_odds, Decimal('2.50'))

    def test_update_single_odds_no_change(self):
        """Test odds update with same value"""
        result = self.service._update_single_odds(
            self.market.pk,
            'Home',
            Decimal('2.50')  # Same as current
        )
        
        self.assertTrue(result['success'])
        self.assertFalse(result['odds'].get('changed', True))

    def test_update_single_odds_invalid_value(self):
        """Test odds update with invalid value"""
        result = self.service._update_single_odds(
            self.market.pk,
            'Home',
            Decimal('0.50')  # Too low
        )
        
        self.assertFalse(result['success'])
        self.assertIn('Invalid odds value', result['error'])

    def test_update_single_odds_not_found(self):
        """Test odds update for non-existent odds"""
        result = self.service._update_single_odds(
            self.market.pk,
            'NonExistent',
            Decimal('2.00')
        )
        
        self.assertFalse(result['success'])
        self.assertIn('Odds not found', result['error'])

    def test_update_event_odds_success(self):
        """Test successful event odds update"""
        odds_data = [
            {
                'market_id': self.market.pk,
                'selection': 'Home',
                'odds_value': 2.60
            },
            {
                'market_id': self.market.pk,
                'selection': 'Draw',
                'odds_value': 3.30
            }
        ]
        
        result = self.service.update_event_odds(self.event.pk, odds_data)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['updated_count'], 2)
        self.assertEqual(len(result['updated_odds']), 2)

    def test_update_event_odds_finished_event(self):
        """Test odds update for finished event"""
        self.event.status = 'finished'
        self.event.save()
        
        odds_data = [
            {
                'market_id': self.market.pk,
                'selection': 'Home',
                'odds_value': 2.60
            }
        ]
        
        result = self.service.update_event_odds(self.event.pk, odds_data)
        
        self.assertFalse(result['success'])
        self.assertIn('Cannot update odds for finished event', result['error'])

    def test_bulk_update_odds(self):
        """Test bulk odds update"""
        updates = [
            {
                'event_id': self.event.pk,
                'odds_data': [
                    {
                        'market_id': self.market.pk,
                        'selection': 'Home',
                        'odds_value': 2.45
                    }
                ]
            }
        ]
        
        result = self.service.bulk_update_odds(updates)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['total_updated'], 1)
        self.assertEqual(result['total_errors'], 0)

    def test_get_odds_changes(self):
        """Test getting odds changes"""
        # Update odds to create changes
        self.odds_home.odds_value = Decimal('2.60')
        self.odds_home.save()
        
        changes = self.service.get_odds_changes(self.event.pk)
        
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0]['selection'], 'Home')
        self.assertEqual(changes[0]['current_odds'], '2.60')
        self.assertEqual(changes[0]['previous_odds'], '2.50')

    def test_calculate_implied_probabilities(self):
        """Test implied probability calculation"""
        result = self.service.calculate_implied_probabilities(self.market.pk)
        
        self.assertEqual(result['market_id'], self.market.pk)
        self.assertIn('Home', result['probabilities'])
        self.assertIn('Draw', result['probabilities'])
        self.assertIn('Away', result['probabilities'])
        
        # Check that probabilities add up to more than 100% (overround)
        self.assertGreater(result['total_probability'], 100)
        self.assertGreater(result['overround'], 0)


class OddsValidationServiceTest(TestCase):
    """Test cases for OddsValidationService"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming',
            external_id='test_event_1'
        )
        self.market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result',
            is_active=True
        )
        self.odds = Odds.objects.create(
            market=self.market,
            selection='Home',
            odds_value=Decimal('2.50'),
            is_active=True
        )

    def test_validate_market_odds_valid(self):
        """Test validation of valid market odds"""
        # Add more odds to make a complete market
        Odds.objects.create(
            market=self.market,
            selection='Draw',
            odds_value=Decimal('3.20'),
            is_active=True
        )
        Odds.objects.create(
            market=self.market,
            selection='Away',
            odds_value=Decimal('2.80'),
            is_active=True
        )
        
        result = OddsValidationService.validate_market_odds(self.market.pk)
        
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['errors']), 0)

    def test_validate_market_odds_too_low(self):
        """Test validation with odds too low"""
        self.odds.odds_value = Decimal('0.50')
        self.odds.save()
        
        result = OddsValidationService.validate_market_odds(self.market.pk)
        
        self.assertFalse(result['valid'])
        self.assertGreater(len(result['errors']), 0)
        self.assertIn('Odds too low', result['errors'][0])

    def test_validate_market_odds_no_odds(self):
        """Test validation with no active odds"""
        self.odds.is_active = False
        self.odds.save()
        
        result = OddsValidationService.validate_market_odds(self.market.pk)
        
        self.assertFalse(result['valid'])
        self.assertIn('No active odds found', result['errors'][0])

    def test_validate_odds_change_valid(self):
        """Test validation of valid odds change"""
        new_odds = Decimal('2.60')
        result = OddsValidationService.validate_odds_change(self.odds.pk, new_odds)
        
        self.assertTrue(result['valid'])
        self.assertEqual(len(result['errors']), 0)

    def test_validate_odds_change_too_low(self):
        """Test validation of odds change with value too low"""
        new_odds = Decimal('0.90')
        result = OddsValidationService.validate_odds_change(self.odds.pk, new_odds)
        
        self.assertFalse(result['valid'])
        self.assertIn('Odds must be at least 1.01', result['errors'][0])

    def test_validate_odds_change_large_change(self):
        """Test validation of large odds change"""
        new_odds = Decimal('5.00')  # 100% increase
        result = OddsValidationService.validate_odds_change(self.odds.pk, new_odds)
        
        self.assertTrue(result['valid'])  # Should be valid but with warning
        self.assertGreater(len(result['warnings']), 0)
        self.assertIn('Large odds change', result['warnings'][0])


class OddsAPITest(APITestCase):
    """Test cases for odds-related API endpoints"""

    client: APIClient  # Type hint for IDE

    def setUp(self):
        """Set up test data"""
        self.admin_user = User.objects.create_user(
            phone_number='+1234567890',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            password='testpass123',
            is_staff=True,
            is_superuser=True
        )
        self.regular_user = User.objects.create_user(
            phone_number='+1234567891',
            email='<EMAIL>',
            first_name='Regular',
            last_name='User',
            password='testpass123'
        )
        
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming',
            external_id='test_event_1'
        )
        self.market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result',
            is_active=True
        )
        self.odds = Odds.objects.create(
            market=self.market,
            selection='Home',
            odds_value=Decimal('2.50'),
            is_active=True
        )

    def test_event_markets_api(self):
        """Test event markets API endpoint"""
        url = reverse('sports_api:event_markets', kwargs={'event_id': self.event.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['event_id'], self.event.pk)
        self.assertEqual(len(data['markets']), 1)
        self.assertEqual(data['markets'][0]['name'], 'Match Result')
        self.assertEqual(len(data['markets'][0]['odds']), 1)

    def test_market_odds_api(self):
        """Test market odds API endpoint"""
        url = reverse('sports_api:market_odds', kwargs={'market_id': self.market.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['market_id'], self.market.pk)
        self.assertEqual(len(data['odds']), 1)
        self.assertEqual(data['odds'][0]['selection'], 'Home')
        self.assertEqual(data['odds'][0]['odds_value'], '2.50')

    def test_update_odds_api_admin_required(self):
        """Test that odds update API requires admin permissions"""
        url = reverse('sports_api:update_odds')
        data = {
            'event_id': self.event.pk,
            'odds_data': [
                {
                    'market_id': self.market.pk,
                    'selection': 'Home',
                    'odds_value': 2.60
                }
            ]
        }
        
        # Test without authentication
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Test with regular user
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_odds_api_success(self):
        """Test successful odds update via API"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('sports_api:update_odds')
        data = {
            'event_id': self.event.id,
            'odds_data': [
                {
                    'market_id': self.market.id,
                    'selection': 'Home',
                    'odds_value': 2.60
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertTrue(response_data['success'])
        self.assertEqual(response_data['updated_count'], 1)
        
        # Verify odds was updated in database
        self.odds.refresh_from_db()
        self.assertEqual(self.odds.odds_value, Decimal('2.60'))

    def test_odds_changes_api(self):
        """Test odds changes API endpoint"""
        # Update odds to create a change
        self.odds.odds_value = Decimal('2.60')
        self.odds.save()
        
        url = reverse('sports_api:odds_changes', kwargs={'event_id': self.event.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['event_id'], self.event.id)
        self.assertEqual(len(data['changes']), 1)
        self.assertEqual(data['changes'][0]['current_odds'], '2.60')

    def test_validate_odds_api(self):
        """Test odds validation API endpoint"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('sports_api:validate_odds')
        data = {'market_id': self.market.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertIn('valid', response_data)
        self.assertIn('errors', response_data)

    def test_bulk_update_odds_api(self):
        """Test bulk odds update API endpoint"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('sports_api:bulk_update_odds')
        data = {
            'updates': [
                {
                    'event_id': self.event.id,
                    'odds_data': [
                        {
                            'market_id': self.market.id,
                            'selection': 'Home',
                            'odds_value': 2.45
                        }
                    ]
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertTrue(response_data['success'])
        self.assertEqual(response_data['total_updated'], 1)


class OddsManagementCommandTest(TestCase):
    """Test cases for odds management command"""

    def setUp(self):
        """Set up test data"""
        self.sport = Sport.objects.create(
            name='Football',
            slug='football',
            is_active=True
        )
        self.event = Event.objects.create(
            sport=self.sport,
            home_team='Team A',
            away_team='Team B',
            start_time=timezone.now() + timedelta(hours=1),
            status='upcoming',
            external_id='test_event_1'
        )
        self.market = Market.objects.create(
            event=self.event,
            market_type='1x2',
            name='Match Result',
            is_active=True
        )
        self.odds = Odds.objects.create(
            market=self.market,
            selection='Home',
            odds_value=Decimal('2.50'),
            is_active=True
        )

    def test_update_odds_command_simulate(self):
        """Test odds update command with simulation"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('update_odds', '--simulate', stdout=out)
        
        output = out.getvalue()
        self.assertIn('Updating odds for:', output)
        
        # Check if odds might have been updated (simulation has randomness)
        self.odds.refresh_from_db()
        # Odds may or may not have changed due to random simulation

    def test_update_odds_command_specific_event(self):
        """Test odds update command for specific event"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('update_odds', f'--event-id={self.event.id}', '--simulate', stdout=out)
        
        output = out.getvalue()
        self.assertIn(f'Updating odds for: {self.event}', output)

    def test_update_odds_command_specific_sport(self):
        """Test odds update command for specific sport"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('update_odds', '--sport=football', '--simulate', stdout=out)
        
        output = out.getvalue()
        self.assertIn('Updating odds for:', output)